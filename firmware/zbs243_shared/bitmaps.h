#ifndef _BITMAPS_H_
#define _BITMAPS_H_

// images generated by https://lvgl.io/tools/imageconverter, prepended with width, height. "CF_INDEXED_1_BIT"-mode, little-endian
#include <stdint.h>

#include "screen.h"

#ifdef ISDEBUGBUILD
static const uint8_t __code debugbuild[] = {
32,11, 
  0x7f, 0xff, 0xff, 0xe0, 
  0x80, 0x00, 0x00, 0x10, 
  0xb9, 0xee, 0x49, 0x90, 
  0xa5, 0x09, 0x4a, 0x50, 
  0xa5, 0x09, 0x4a, 0x10, 
  0xa5, 0xce, 0x4a, 0xd0, 
  0xa5, 0x09, 0x4a, 0x50, 
  0xa5, 0x09, 0x4a, 0x50, 
  0xb9, 0xee, 0x31, 0x90, 
  0x80, 0x00, 0x00, 0x10, 
  0x7f, 0xff, 0xff, 0xe0, 
};
#endif

#ifndef LEAN_VERSION

static const uint8_t __code oepli[] = {
  128, 26,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x1c, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x9f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x38, 0x70, 0x01, 0xc0, 
  0x7f, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x9f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x38, 0x70, 0x01, 0xc0, 
  0x7f, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x9f, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x38, 0x70, 0x01, 0xc0, 
  0xf7, 0x80, 0x00, 0x00, 0x00, 0x1c, 0x1c, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x01, 0xc0, 
  0xe3, 0x80, 0x00, 0x00, 0x00, 0x1c, 0x1c, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x01, 0xc0, 
  0xe3, 0x9d, 0xc1, 0xc7, 0x70, 0x1f, 0x9c, 0xe7, 0x73, 0xb8, 0x71, 0xd0, 0x38, 0x73, 0xb9, 0xc7, 
  0xe3, 0x9f, 0xe3, 0xe7, 0xf8, 0x1f, 0x9f, 0xef, 0xf3, 0xfc, 0xf9, 0xf0, 0x38, 0x73, 0xfd, 0xce, 
  0xe3, 0x9f, 0xe7, 0x77, 0xf8, 0x1f, 0x9f, 0xcf, 0xf3, 0xfd, 0xdd, 0xf0, 0x38, 0x73, 0xfd, 0xdc, 
  0xe3, 0x9c, 0xe7, 0xf7, 0x38, 0x1c, 0x1f, 0x8e, 0x73, 0x9d, 0xfd, 0xc0, 0x38, 0x73, 0x9d, 0xf8, 
  0xe3, 0x9c, 0xe7, 0xf7, 0x38, 0x1c, 0x1c, 0x0e, 0x73, 0x9d, 0xfd, 0xc0, 0x38, 0x73, 0x9d, 0xf8, 
  0xf7, 0x9c, 0xe7, 0x07, 0x38, 0x1c, 0x1c, 0x0e, 0x73, 0x9d, 0xc1, 0xc0, 0x38, 0x73, 0x9d, 0xdc, 
  0x7f, 0x1f, 0xe7, 0xf7, 0x38, 0x1f, 0x9c, 0x0f, 0xf3, 0xfd, 0xfd, 0xc0, 0x3f, 0x73, 0x9d, 0xdc, 
  0x7f, 0x1f, 0xe3, 0xe7, 0x38, 0x1f, 0x9c, 0x0f, 0xf3, 0xfc, 0xf9, 0xc0, 0x3f, 0x73, 0x9d, 0xce, 
  0x1e, 0x1d, 0xc1, 0xc7, 0x38, 0x1f, 0x9c, 0x07, 0x73, 0xb8, 0x71, 0xc0, 0x3f, 0x73, 0x9d, 0xc7, 
  0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

static const uint8_t __code cloud[] = {
  128, 50, 
  0x00, 0x08, 0x82, 0xa2, 0xab, 0x55, 0xbf, 0xff, 0xff, 0xff, 0x7d, 0xb4, 0x62, 0x28, 0x00, 0x00, 
  0x00, 0x10, 0x10, 0x11, 0x76, 0xff, 0x7b, 0xff, 0xff, 0x7f, 0xb7, 0xa9, 0x55, 0x50, 0x51, 0x00, 
  0x00, 0x29, 0x22, 0x96, 0x6f, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xea, 0xe6, 0x22, 0xaa, 0x08, 0x00, 
  0x00, 0x02, 0x54, 0x2a, 0xbf, 0x3f, 0x7f, 0xff, 0xff, 0xff, 0xfd, 0x59, 0xb4, 0x10, 0x20, 0x00, 
  0x00, 0x24, 0x23, 0xb5, 0xb5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xae, 0x48, 0x55, 0x10, 0x00, 
  0x00, 0x88, 0x90, 0x54, 0x6a, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xf5, 0xa3, 0x82, 0x00, 0x00, 
  0x00, 0x01, 0x2d, 0xa3, 0xb3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xbe, 0x4e, 0x52, 0x09, 0x00, 
  0x04, 0x90, 0x02, 0x0d, 0x55, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xdd, 0x68, 0xa9, 0x00, 0x10, 
  0x00, 0x49, 0x0c, 0xfb, 0xab, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xeb, 0x95, 0x5a, 0x00, 0x20, 
  0x00, 0x12, 0x91, 0x22, 0xbf, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x65, 0x81, 0x40, 0x40, 
  0x00, 0x28, 0x12, 0x7d, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xab, 0x28, 0x00, 0x00, 
  0x00, 0x42, 0x0a, 0x97, 0x75, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x4a, 0xc2, 0x04, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x08, 0x02, 0x9c, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6a, 0x6a, 0x8d, 0x00, 0x20, 
  0x00, 0x04, 0x4d, 0x72, 0xaa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0xd5, 0x74, 0x80, 0x00, 
  0x02, 0x40, 0x12, 0x8e, 0xdf, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xae, 0xaa, 0x0a, 0x11, 0x20, 
  0x00, 0x20, 0x52, 0x5f, 0xf5, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x72, 0x94, 0xaa, 0x24, 0x00, 
  0x00, 0x01, 0x0a, 0xab, 0x6f, 0x77, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xaf, 0x54, 0x54, 0x88, 0x00, 
  0x00, 0x04, 0x80, 0x4d, 0x95, 0xeb, 0xff, 0xff, 0xff, 0xff, 0x5b, 0x58, 0xb5, 0x51, 0x40, 0x00, 
  0x00, 0x08, 0x08, 0xa6, 0xb3, 0xf7, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xad, 0x4a, 0xa0, 0x10, 0x00, 
  0x00, 0x02, 0x96, 0x41, 0xdc, 0xae, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xbf, 0xea, 0x48, 0x02, 0x00, 
  0x00, 0x00, 0x09, 0x3e, 0xab, 0x75, 0xef, 0xff, 0xff, 0xff, 0xff, 0xd4, 0xb1, 0x21, 0x08, 0x00, 
  0x00, 0x00, 0x0a, 0xc3, 0x5d, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xef, 0xef, 0x44, 0x4a, 0x64, 0x80, 
  0x00, 0x00, 0xa1, 0x52, 0xa6, 0xef, 0xef, 0xff, 0xff, 0xff, 0xd6, 0xca, 0xf5, 0x10, 0x18, 0x00, 
  0x00, 0x00, 0x02, 0x15, 0x4b, 0xd5, 0xdf, 0xff, 0xff, 0xf7, 0xeb, 0xd4, 0xd8, 0x85, 0x00, 0x00, 
};

static const uint8_t __code receive[] = {
  56, 56, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 
  0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0xf0, 
  0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf0, 
  0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xf0, 
  0x00, 0x00, 0x00, 0x01, 0xff, 0xff, 0xf0, 
  0x00, 0x00, 0x00, 0x07, 0xff, 0xff, 0xc0, 
  0x00, 0x00, 0x00, 0x1f, 0xff, 0x80, 0x00, 
  0x00, 0x00, 0x00, 0x7f, 0xf8, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xff, 0xe0, 0x00, 0x00, 
  0x00, 0x00, 0x01, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x07, 0xfe, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x0f, 0xf8, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x1f, 0xe0, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x3f, 0xc0, 0x00, 0x03, 0xe0, 
  0x00, 0x00, 0x7f, 0x80, 0x00, 0x7f, 0xf0, 
  0x00, 0x00, 0xfe, 0x00, 0x03, 0xff, 0xf0, 
  0x00, 0x01, 0xfc, 0x00, 0x0f, 0xff, 0xf0, 
  0x00, 0x01, 0xf8, 0x00, 0x3f, 0xff, 0xf0, 
  0x00, 0x03, 0xf8, 0x00, 0xff, 0xff, 0x80, 
  0x00, 0x07, 0xf0, 0x01, 0xff, 0xe0, 0x00, 
  0x00, 0x0f, 0xe0, 0x03, 0xff, 0x00, 0x00, 
  0x00, 0x0f, 0xc0, 0x0f, 0xf8, 0x00, 0x00, 
  0x00, 0x1f, 0x80, 0x1f, 0xf0, 0x00, 0x00, 
  0x00, 0x1f, 0x80, 0x3f, 0xc0, 0x00, 0x00, 
  0x00, 0x3f, 0x00, 0x3f, 0x80, 0x00, 0x00, 
  0x00, 0x3f, 0x00, 0x7f, 0x00, 0x00, 0x00, 
  0x00, 0x7e, 0x00, 0xfe, 0x00, 0x07, 0xe0, 
  0x00, 0x7e, 0x01, 0xfc, 0x00, 0x1f, 0xf0, 
  0x00, 0xfc, 0x01, 0xf8, 0x00, 0x7f, 0xf0, 
  0x00, 0xfc, 0x03, 0xf0, 0x01, 0xff, 0xf0, 
  0x00, 0xf8, 0x03, 0xf0, 0x03, 0xff, 0xf0, 
  0x01, 0xf8, 0x07, 0xe0, 0x07, 0xff, 0x00, 
  0x01, 0xf8, 0x07, 0xe0, 0x0f, 0xf0, 0x00, 
  0x01, 0xf0, 0x0f, 0xc0, 0x1f, 0xe0, 0x00, 
  0x01, 0xf0, 0x0f, 0xc0, 0x3f, 0x80, 0x00, 
  0x03, 0xf0, 0x0f, 0x80, 0x3f, 0x00, 0x00, 
  0x03, 0xf0, 0x1f, 0x80, 0x7e, 0x00, 0x00, 
  0x03, 0xe0, 0x1f, 0x80, 0x7e, 0x00, 0x00, 
  0x03, 0xe0, 0x1f, 0x00, 0xfc, 0x01, 0xe0, 
  0x03, 0xe0, 0x1f, 0x00, 0xfc, 0x07, 0xf8, 
  0x03, 0xe0, 0x1f, 0x00, 0xf8, 0x0f, 0xfc, 
  0x03, 0xe0, 0x3f, 0x00, 0xf8, 0x0f, 0xfc, 
  0x03, 0xe0, 0x3f, 0x01, 0xf8, 0x1f, 0xfe, 
  0x03, 0xe0, 0x3f, 0x01, 0xf8, 0x1f, 0xfe, 
  0x03, 0xe0, 0x3f, 0x01, 0xf8, 0x1f, 0xfe, 
  0x03, 0xc0, 0x3e, 0x01, 0xf0, 0x1f, 0xfe, 
  0x01, 0xc0, 0x1e, 0x00, 0xf0, 0x0f, 0xfc, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfc, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf8, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
};


static const uint8_t __code failed[] = {
  48, 48,
  0x00, 0x00, 0x1f, 0xf8, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 
  0x00, 0x03, 0xff, 0xff, 0xe0, 0x00, 
  0x00, 0x0f, 0xff, 0xff, 0xf0, 0x00, 
  0x00, 0x3f, 0xff, 0xff, 0xfc, 0x00, 
  0x00, 0x7f, 0xf0, 0x0f, 0xfe, 0x00, 
  0x00, 0xff, 0x80, 0x01, 0xff, 0x00, 
  0x01, 0xfe, 0x00, 0x00, 0x7f, 0x80, 
  0x03, 0xf8, 0x00, 0x00, 0x1f, 0xc0, 
  0x07, 0xf0, 0x00, 0x00, 0x3f, 0xe0, 
  0x0f, 0xe0, 0x00, 0x00, 0x7f, 0xf0, 
  0x0f, 0xc0, 0x00, 0x00, 0xff, 0xf0, 
  0x1f, 0x80, 0x00, 0x01, 0xff, 0xf8, 
  0x1f, 0x00, 0x00, 0x03, 0xff, 0xf8, 
  0x3f, 0x00, 0x00, 0x07, 0xfe, 0xfc, 
  0x3e, 0x00, 0x00, 0x0f, 0xfc, 0x7c, 
  0x7e, 0x00, 0x00, 0x1f, 0xf8, 0x7e, 
  0x7c, 0x00, 0x00, 0x3f, 0xf0, 0x3e, 
  0x7c, 0x00, 0x00, 0x7f, 0xe0, 0x3e, 
  0xfc, 0x00, 0x00, 0xff, 0xc0, 0x3f, 
  0xf8, 0x00, 0x01, 0xff, 0x80, 0x1f, 
  0xf8, 0x00, 0x03, 0xff, 0x00, 0x1f, 
  0xf8, 0x00, 0x07, 0xfe, 0x00, 0x1f, 
  0xf8, 0x00, 0x0f, 0xfc, 0x00, 0x1f, 
  0xf8, 0x00, 0x1f, 0xf8, 0x00, 0x1f, 
  0xf8, 0x00, 0x3f, 0xf0, 0x00, 0x1f, 
  0xf8, 0x00, 0x7f, 0xe0, 0x00, 0x1f, 
  0xf8, 0x00, 0xff, 0xc0, 0x00, 0x1f, 
  0xfc, 0x01, 0xff, 0x80, 0x00, 0x3f, 
  0x7c, 0x03, 0xff, 0x00, 0x00, 0x3e, 
  0x7c, 0x07, 0xfe, 0x00, 0x00, 0x3e, 
  0x7e, 0x0f, 0xfc, 0x00, 0x00, 0x7e, 
  0x3e, 0x1f, 0xf8, 0x00, 0x00, 0x7c, 
  0x3f, 0x3f, 0xf0, 0x00, 0x00, 0xfc, 
  0x3f, 0x7f, 0xe0, 0x00, 0x00, 0xfc, 
  0x1f, 0xff, 0xc0, 0x00, 0x01, 0xf8, 
  0x0f, 0xff, 0x80, 0x00, 0x03, 0xf0, 
  0x0f, 0xff, 0x00, 0x00, 0x07, 0xf0, 
  0x07, 0xfe, 0x00, 0x00, 0x0f, 0xe0, 
  0x03, 0xfc, 0x00, 0x00, 0x1f, 0xc0, 
  0x01, 0xfe, 0x00, 0x00, 0x7f, 0x80, 
  0x00, 0xff, 0x80, 0x01, 0xff, 0x00, 
  0x00, 0x7f, 0xf0, 0x0f, 0xfe, 0x00, 
  0x00, 0x3f, 0xff, 0xff, 0xfc, 0x00, 
  0x00, 0x0f, 0xff, 0xff, 0xf0, 0x00, 
  0x00, 0x03, 0xff, 0xff, 0xe0, 0x00, 
  0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x1f, 0xf8, 0x00, 0x00, 
};
#endif

#if (SCREEN_WIDTH != 128) 
static const uint8_t __code ant[] = {
  16, 16,
  0x00, 0x40, 
  0x02, 0x20, 
  0x01, 0x20, 
  0x11, 0x20, 
  0x11, 0x20, 
  0x12, 0x20, 
  0x28, 0x40, 
  0x28, 0x00, 
  0x28, 0x00, 
  0x44, 0x00, 
  0x44, 0x00, 
  0x44, 0x00, 
  0x44, 0x00, 
  0x82, 0x00, 
  0x82, 0x00, 
  0xfe, 0x00, 
};
#else
static const uint8_t __code ant[] = {
  // rotated 90 degrees
  16,16,
  0x00, 0x00, 
  0x00, 0x00, 
  0x00, 0x00, 
  0x00, 0x00, 
  0x00, 0x00, 
  0x7c, 0x00, 
  0x82, 0x00, 
  0x00, 0x00, 
  0x38, 0x00, 
  0x44, 0x07, 
  0x00, 0x79, 
  0x03, 0x81, 
  0x1c, 0x01, 
  0x03, 0x81, 
  0x00, 0x79, 
  0x00, 0x07, 
};
#endif
static const uint8_t __code cross[] = {
  8,8,
  0x00, 
  0x63, 
  0x77, 
  0x3e, 
  0x1c, 
  0x3e, 
  0x77, 
  0x63
};

#if (SCREEN_WIDTH != 128) 
static const uint8_t __code battery[] = {
  16,10,
  0x00, 0x00, 
  0x7f, 0xfc, 
  0x40, 0x04, 
  0x58, 0x06, 
  0x58, 0x06, 
  0x58, 0x06, 
  0x58, 0x06, 
  0x40, 0x04, 
  0x7f, 0xfc, 
  0x00, 0x00, 
};
#else
// this battery symbol is rotated 90'
static const uint8_t __code battery[] = {
16,16,
  0x00, 0x00, 
  0x03, 0xc0, 
  0x0f, 0xf0, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x08, 0x10, 
  0x0b, 0xd0, 
  0x0b, 0xd0, 
  0x08, 0x10, 
  0x0f, 0xf0, 
  0x00, 0x00, 
};

#endif

#endif