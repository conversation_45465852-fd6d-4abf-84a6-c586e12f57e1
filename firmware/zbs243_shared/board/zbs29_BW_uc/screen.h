#ifndef _SCREEN_H_
#define _SCREEN_H_

#include <stdbool.h>
#include <stdint.h>
#include "../uc8151.h"

#define BW_SCREEN
#define SCREEN_WIDTH				128
#define SCREEN_HEIGHT				296

#define SCREEN_NUM_GREYS			1
#define SCREEN_FIRST_GREY_IDX		0
#define SCREEN_EXTRA_COLOR_INDEX	-1		//set to negative if nonexistent
#define SCREEN_TX_BPP				4		//in transit

#define SCREEN_WIDTH_MM				29
#define SCREEN_HEIGHT_MM			67

#define SCREEN_BYTE_FILL			0x44	//white

#define SCREEN_TYPE					TagScreenEink_BW_1bpp

#define SCREEN_DATA_PASSES			2

#endif
