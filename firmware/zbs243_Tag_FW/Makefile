
BUILD		?= zbs29_ssd1619

#file containing main() must be first!
SOURCES		+= main.c eeprom.c drawing.c
SOURCES		+= ccm.c comms.c
SOURCES		+= syncedproto.c userinterface.c
SOURCES		+= powermgt.c barcode.c settings.c
SOURCES		+= i2cdevices.c

include board/$(BUILD)/make.mk
include soc/$(SOC)/make.mk
include cpu/$(CPU)/make.mk
FLAGS += -Iboard/$(BUILD)
FLAGS += -Isoc/$(SOC)
FLAGS += -Icpu/$(CPU)
FLAGS += -I../../
FLAGS += -I.

SOURCES += cpu/$(CPU)/cpu.c
SOURCES += board/$(BUILD)/board.c
SOURCES += board/$(BUILD)/screen.c
SOURCES += md5.c

OUTPUT_NAME = EPD_Thermometer_$(BUILD)
BUILD_DIR = .build
OUTPUT_DIR = .output
OBJECTS = $(patsubst %.c,$(BUILD_DIR)/%.rel,$(SOURCES))
OUTPUT = $(OUTPUT_DIR)/$(OUTPUT_NAME)

all: size #make sure this is the first target

# Put all build output in a separate directory

$(BUILD_DIR)/%.$(OBJFILEEXT): %.c
	@mkdir -p $(dir $@)
	$(CC) $(FLAGS) -c $^ -o $@

$(OUTPUT).ihx: $(OBJECTS)
	@mkdir -p $(dir $(OUTPUT))
	$(CC) $(FLAGS) --out-fmt-ihx -o $(OUTPUT).ihx $(OBJECTS)

$(OUTPUT).bin: $(OUTPUT).ihx
# 	makebin -p $(OUTPUT).ihx $(OUTPUT).bin
	objcopy $^ $@ -I ihex -O binary

build: $(OUTPUT).bin

size: build
	@echo '---------- Segments ----------'
	@egrep '(ABS,CON)|(REL,CON)' $(OUTPUT).map | gawk --non-decimal-data '{dec = sprintf("%d","0x" $$2); print dec " " $$0}' | /usr/bin/sort -n -k1 | cut -f2- -d' '
	@echo '------------------------------'
	@stat -L --printf "Size of $(OUTPUT).bin: %s bytes\n" $(OUTPUT).bin

.PHONY: clean

clean:
	rm -r -f $(BUILD_DIR) $(OUTPUT_DIR)
