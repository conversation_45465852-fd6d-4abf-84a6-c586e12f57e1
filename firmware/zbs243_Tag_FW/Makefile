
BUILD		?= zbs29_ssd1619

#file containing main() must be first!
SOURCES		+= main.c eeprom.c drawing.c
#SOURCES		+= ccm.c comms.c
SOURCES		+= comms.c
SOURCES		+= syncedproto.c userinterface.c
SOURCES		+= powermgt.c barcode.c settings.c
SOURCES		+= i2cdevices.c



all:	#make sure it is the first target

include board/$(BUILD)/make.mk
include soc/$(SOC)/make.mk
include cpu/$(CPU)/make.mk
FLAGS += -Iboard/$(BUILD)
FLAGS += -Isoc/$(SOC)
FLAGS += -Icpu/$(CPU)
FLAGS += -I../../

SOURCES += cpu/$(CPU)/cpu.c
SOURCES += board/$(BUILD)/board.c
SOURCES += board/$(BUILD)/screen.c

SOURCES += md5.c


EEPROMDRV ?= eeprom.c



SOURCES += $(EEPROMDRV)
FLAGS += -I.


BUILD_DIR = .build
OUTPUT_DIR = .output
OBJECTS = $(patsubst %.c,$(BUILD_DIR)/%.rel,$(SOURCES))

all: $(TARGETS)

print-%: ; @echo $* = $($*)

# Put all build output in a separate directory

$(BUILD_DIR)/%.$(OBJFILEEXT): %.c
	@mkdir -p $(dir $@)
	$(CC) -c $^ $(FLAGS) -o $@

main.ihx: $(OBJECTS)
	rm -f $@
	$(CC) $^ $(FLAGS)

main.elf: $(OBJECTS)
	$(CC) $(FLAGS) -o $@ $^

%.bin: %.ihx
	objcopy $^ $@ -I ihex -O binary

size: main.elf
	@echo '---------- Segments ----------'
	@egrep '(ABS,CON)|(REL,CON)' main.map | gawk --non-decimal-data '{dec = sprintf("%d","0x" $$2); print dec " " $$0}' | /usr/bin/sort -n -k1 | cut -f2- -d' '
	@echo '------------------------------'
	@stat -L --printf "Size of main.bin: %s bytes\n" main.bin

.PHONY: clean

clean:
	rm -r -f $(BUILD_DIR) $(OUTPUT_DIR)
